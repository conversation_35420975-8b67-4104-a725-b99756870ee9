import type { ComponentPublicInstance, ExtractPropTypes, MaybeRefOrGetter, PropType, Slot } from "vue";
export declare const vueToPrintProps: () => {
    /**
     *  Class to pass to the print window body
     */
    readonly bodyClass: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    /**
     *  Content to be printed
     */
    readonly content: {
        readonly type: PropType<HTMLElement>;
        readonly required: true;
    };
    /**
     *  Copy styles over into print window. default: true
     */
    readonly copyStyles: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    /**
     * Set the title for printing when saving as a file.
     * Will result in the calling page's `<title>` being temporarily changed while printing.
     */
    readonly documentTitle: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    /**
     *  Pre-load these fonts to ensure availability when printing
     */
    readonly fonts: {
        readonly type: PropType<Font[]>;
        readonly default: () => never[];
    };
    /**
     *  Callback function to trigger after print
     */
    readonly onAfterPrint: {
        readonly type: PropType<() => void>;
        readonly default: null;
    };
    /**
     *  Callback function to trigger before page content is retrieved for printing
     */
    readonly onBeforeGetContent: {
        readonly type: PropType<() => void | Promise<void>>;
        readonly default: null;
    };
    /**
     *  Callback function to trigger before print
     */
    readonly onBeforePrint: {
        readonly type: PropType<() => void | Promise<void>>;
        readonly default: null;
    };
    /**
     *  Callback function to listen for printing errors
     */
    readonly onPrintError: {
        readonly type: PropType<(errorLocation: "onBeforeGetContent" | "onBeforePrint" | "print", error: Error) => void>;
        readonly default: null;
    };
    /**
     *  Override default print window styling
     */
    readonly pageStyle: {
        readonly type: PropType<string | PropertyFunction<string>>;
        readonly default: "\n        @page {\n            /* Remove browser default header (title) and footer (url) */\n            margin: 0;\n        }\n        @media print {\n            body {\n                /* Tell browsers to print background colors */\n                -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge/Opera */\n                color-adjust: exact; /* Firefox */\n            }\n        }\n    ";
    };
    /**
     *  Override the default `window.print` method that is used for printing
     */
    readonly print: {
        readonly type: PropType<(target: HTMLIFrameElement) => Promise<void>>;
        readonly default: null;
    };
    /**
     * Remove the iframe after printing.
     * NOTE: `onAfterPrint` will run before the iframe is removed
     */
    readonly removeAfterPrint: {
        readonly type: BooleanConstructor;
        readonly default: false;
    };
    /**
     *  Suppress error messages
     */
    readonly suppressErrors: {
        readonly type: BooleanConstructor;
        readonly default: false;
    };
    /**
     *  Set the nonce attribute for whitelisting script and style -elements for CSP (content security policy)
     */
    readonly nonce: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    readonly contextSlots: {
        readonly type: PropType<VueToPrintSlots>;
    };
};
/**
 * 内部使用的 props 类型定义.
 */
export type InnerVueToPrintProps = ExtractPropTypes<ReturnType<typeof vueToPrintProps>>;
/**
 * @see {@link vueToPrintProps}
 */
export interface UseVueToPrintProps {
    bodyClass: MaybeRefOrGetter<string>;
    content: MaybeRefOrGetter<HTMLElement | ComponentPublicInstance>;
    copyStyles: MaybeRefOrGetter<boolean>;
    documentTitle: MaybeRefOrGetter<string>;
    fonts: MaybeRefOrGetter<Font[]>;
    onAfterPrint: () => void;
    onBeforeGetContent: () => void | Promise<void>;
    onBeforePrint: () => void | Promise<void>;
    onPrintError: (errorLocation: "onBeforeGetContent" | "onBeforePrint" | "print", error: Error) => void;
    pageStyle: MaybeRefOrGetter<string | PropertyFunction<string>>;
    print: (target: HTMLIFrameElement) => void | Promise<void>;
    removeAfterPrint: MaybeRefOrGetter<boolean>;
    suppressErrors: MaybeRefOrGetter<boolean>;
    nonce: MaybeRefOrGetter<string>;
}
export type PublicUseVueToPrintProps = Partial<Omit<UseVueToPrintProps, "content">> & Pick<UseVueToPrintProps, "content">;
/**
 * 组件对外暴露的 props 类型定义.
 */
export type VueToPrintExpose = {
    handlePrint: () => void;
};
export type VueToPrintSlots = {
    default: Slot;
    trigger: Slot;
};
export type VueToPrintInstance = ComponentPublicInstance<InnerVueToPrintProps, VueToPrintExpose>;
export type Font = {
    family: string;
    source: string;
    weight?: string;
    style?: string;
};
export type PropertyFunction<T> = () => T;
