declare const _default_1: import("vue").DefineComponent<{
    readonly bodyClass: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    readonly content: {
        readonly type: import("vue").PropType<HTMLElement>;
        readonly required: true;
    };
    readonly copyStyles: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly documentTitle: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    readonly fonts: {
        readonly type: import("vue").PropType<import("./types").Font[]>;
        readonly default: () => never[];
    };
    readonly onAfterPrint: {
        readonly type: import("vue").PropType<() => void>;
        readonly default: null;
    };
    readonly onBeforeGetContent: {
        readonly type: import("vue").PropType<() => void | Promise<void>>;
        readonly default: null;
    };
    readonly onBeforePrint: {
        readonly type: import("vue").PropType<() => void | Promise<void>>;
        readonly default: null;
    };
    readonly onPrintError: {
        readonly type: import("vue").PropType<(errorLocation: "onBeforeGetContent" | "onBeforePrint" | "print", error: Error) => void>;
        readonly default: null;
    };
    readonly pageStyle: {
        readonly type: import("vue").PropType<string | import("./types").PropertyFunction<string>>;
        readonly default: "\n        @page {\n            /* Remove browser default header (title) and footer (url) */\n            margin: 0;\n        }\n        @media print {\n            body {\n                /* Tell browsers to print background colors */\n                -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge/Opera */\n                color-adjust: exact; /* Firefox */\n            }\n        }\n    ";
    };
    readonly print: {
        readonly type: import("vue").PropType<(target: HTMLIFrameElement) => Promise<void>>;
        readonly default: null;
    };
    readonly removeAfterPrint: {
        readonly type: BooleanConstructor;
        readonly default: false;
    };
    readonly suppressErrors: {
        readonly type: BooleanConstructor;
        readonly default: false;
    };
    readonly nonce: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    readonly contextSlots: {
        readonly type: import("vue").PropType<import("./types").VueToPrintSlots>;
    };
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>[] | undefined, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly bodyClass: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    readonly content: {
        readonly type: import("vue").PropType<HTMLElement>;
        readonly required: true;
    };
    readonly copyStyles: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly documentTitle: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    readonly fonts: {
        readonly type: import("vue").PropType<import("./types").Font[]>;
        readonly default: () => never[];
    };
    readonly onAfterPrint: {
        readonly type: import("vue").PropType<() => void>;
        readonly default: null;
    };
    readonly onBeforeGetContent: {
        readonly type: import("vue").PropType<() => void | Promise<void>>;
        readonly default: null;
    };
    readonly onBeforePrint: {
        readonly type: import("vue").PropType<() => void | Promise<void>>;
        readonly default: null;
    };
    readonly onPrintError: {
        readonly type: import("vue").PropType<(errorLocation: "onBeforeGetContent" | "onBeforePrint" | "print", error: Error) => void>;
        readonly default: null;
    };
    readonly pageStyle: {
        readonly type: import("vue").PropType<string | import("./types").PropertyFunction<string>>;
        readonly default: "\n        @page {\n            /* Remove browser default header (title) and footer (url) */\n            margin: 0;\n        }\n        @media print {\n            body {\n                /* Tell browsers to print background colors */\n                -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge/Opera */\n                color-adjust: exact; /* Firefox */\n            }\n        }\n    ";
    };
    readonly print: {
        readonly type: import("vue").PropType<(target: HTMLIFrameElement) => Promise<void>>;
        readonly default: null;
    };
    readonly removeAfterPrint: {
        readonly type: BooleanConstructor;
        readonly default: false;
    };
    readonly suppressErrors: {
        readonly type: BooleanConstructor;
        readonly default: false;
    };
    readonly nonce: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    readonly contextSlots: {
        readonly type: import("vue").PropType<import("./types").VueToPrintSlots>;
    };
}>>, {
    readonly onBeforeGetContent: () => void | Promise<void>;
    readonly onBeforePrint: () => void | Promise<void>;
    readonly print: (target: HTMLIFrameElement) => Promise<void>;
    readonly bodyClass: string;
    readonly copyStyles: boolean;
    readonly documentTitle: string;
    readonly fonts: import("./types").Font[];
    readonly onAfterPrint: () => void;
    readonly onPrintError: (errorLocation: "onBeforeGetContent" | "onBeforePrint" | "print", error: Error) => void;
    readonly pageStyle: string | import("./types").PropertyFunction<string>;
    readonly removeAfterPrint: boolean;
    readonly suppressErrors: boolean;
    readonly nonce: string;
}, {}>;
export default _default_1;
