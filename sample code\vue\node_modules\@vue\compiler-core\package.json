{"name": "@vue/compiler-core", "version": "3.5.17", "description": "@vue/compiler-core", "main": "index.js", "module": "dist/compiler-core.esm-bundler.js", "types": "dist/compiler-core.d.ts", "files": ["index.js", "dist"], "exports": {".": {"types": "./dist/compiler-core.d.ts", "node": {"production": "./dist/compiler-core.cjs.prod.js", "development": "./dist/compiler-core.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-core.esm-bundler.js", "import": "./dist/compiler-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "buildOptions": {"name": "VueCompilerCore", "compat": true, "formats": ["esm-bundler", "cjs"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-core"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-core#readme", "dependencies": {"@babel/parser": "^7.27.5", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1", "@vue/shared": "3.5.17"}, "devDependencies": {"@babel/types": "^7.27.6"}}