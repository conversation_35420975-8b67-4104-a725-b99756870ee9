<template>
  <div class="jscanify-container">
    <div class="header">
      <h1>📄 JSCanify Document Scanner</h1>
      <p>Upload a photo and automatically detect and crop documents</p>
    </div>

    <!-- Document Scanner Section -->
    <div class="input-section">
      <h2>📷 Upload Document Photo</h2>

      <div class="upload-area" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          @change="handleFileSelect"
          style="display: none"
        />

        <div v-if="!uploadedImage" class="upload-placeholder" @click="$refs.fileInput.click()">
          <div class="upload-icon">📷</div>
          <p>Click to upload or drag & drop an image</p>
          <p class="upload-hint">Supports JPG, PNG, WebP formats</p>
        </div>

        <div v-if="uploadedImage" class="image-preview">
          <img :src="uploadedImage" alt="Uploaded document" />
          <div class="image-actions">
            <button @click="cropDocument" class="btn-primary" :disabled="isProcessing || !isLibraryReady">
              <span v-if="isProcessing">🔄 Processing...</span>
              <span v-else-if="!isLibraryReady">⏳ Loading Scanner...</span>
              <span v-else>✂️ Auto Crop Document</span>
            </button>
            <button @click="clearImage" class="btn-secondary">
              🗑️ Remove Image
            </button>
          </div>
        </div>
      </div>

      <!-- Document Detection Results -->
      <div v-if="highlightCanvas || extractCanvas" class="scan-results">

        <!-- Highlighted Detection -->
        <div v-if="highlightCanvas" class="result-item">
          <h3>🔍 Document Detection</h3>
          <div class="canvas-container">
            <canvas ref="highlightCanvas" class="result-canvas"></canvas>
          </div>
        </div>

        <!-- Extracted Document -->
        <div v-if="extractCanvas" class="result-item">
          <h3>📄 Extracted Document</h3>
          <div class="canvas-container">
            <canvas ref="extractCanvas" class="result-canvas"></canvas>
          </div>
        </div>

      </div>

      <div v-if="croppedImage" class="cropped-result">
        <h3>✨ Cropped Document</h3>
        <div class="cropped-preview">
          <img :src="croppedImage" alt="Cropped document" />
          <div class="crop-actions">
            <button @click="downloadCropped" class="btn-primary">
              💾 Download Cropped Image
            </button>
            <button @click="extractText" class="btn-secondary" :disabled="isExtractingText">
              {{ isExtractingText ? 'Extracting...' : '📝 Extract Text (OCR)' }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="extractedText" class="extracted-text">
        <h3>📝 Extracted Text</h3>
        <div class="text-content">
          <textarea v-model="extractedText" readonly></textarea>
          <button @click="copyText" class="btn-secondary">
            📋 Copy Text
          </button>
        </div>
      </div>
    </div>
    
    <transition name="fade">
      <div v-if="errorMessage" class="error-message">
        <strong>❌ Error:</strong> {{ errorMessage }}
      </div>
    </transition>
    
    <transition name="fade">
      <div v-if="successMessage" class="success-message">
        <strong>✅ Success:</strong> {{ successMessage }}
      </div>
    </transition>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Document Scanner State
const uploadedImage = ref(null)
const croppedImage = ref(null)
const extractedText = ref('')
const isProcessing = ref(false)
const isExtractingText = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// JScanify Library State
const isLibraryReady = ref(false)
const scanner = ref(null)
const currentImageElement = ref(null)
const highlightCanvas = ref(null)
const extractCanvas = ref(null)

// Initialize JScanify library
const initializeJScanify = () => {
  return new Promise((resolve, reject) => {
    // Load OpenCV first
    const openCVScript = document.createElement('script')
    openCVScript.src = 'https://docs.opencv.org/4.7.0/opencv.js'
    openCVScript.async = true

    openCVScript.onload = () => {
      console.log('OpenCV loaded, waiting for initialization...')

      const checkCV = () => {
        if (typeof window.cv !== 'undefined' && window.cv.Mat) {
          // Load JScanify
          const jscanifyScript = document.createElement('script')
          jscanifyScript.src = 'https://cdn.jsdelivr.net/gh/ColonelParrot/jscanify@master/src/jscanify.min.js'
          jscanifyScript.onload = () => {
            try {
              scanner.value = new window.jscanify()
              isLibraryReady.value = true
              console.log('✅ JScanify library loaded successfully')
              resolve()
            } catch (error) {
              console.error('❌ Failed to initialize JScanify:', error)
              reject(error)
            }
          }
          jscanifyScript.onerror = () => {
            const error = new Error('Failed to load JScanify library')
            console.error('❌', error.message)
            reject(error)
          }
          document.head.appendChild(jscanifyScript)
        } else {
          setTimeout(checkCV, 100)
        }
      }
      checkCV()
    }

    openCVScript.onerror = () => {
      const error = new Error('Failed to load OpenCV library')
      console.error('❌', error.message)
      reject(error)
    }

    document.head.appendChild(openCVScript)
  })
}

// Initialize on component mount
onMounted(async () => {
  try {
    await initializeJScanify()
  } catch (error) {
    console.error('Failed to initialize document scanner:', error)
  }
})
const fileInput = ref(null)

const clearMessages = () => {
  errorMessage.value = ''
  successMessage.value = ''
}

// Document Scanner Functions
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

const handleDrop = (event) => {
  event.preventDefault()
  const file = event.dataTransfer.files[0]
  if (file && file.type.startsWith('image/')) {
    processFile(file)
  }
}

const processFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    uploadedImage.value = e.target.result
    croppedImage.value = null
    extractedText.value = ''
    clearMessages()
  }
  reader.readAsDataURL(file)
}

const clearImage = () => {
  uploadedImage.value = null
  croppedImage.value = null
  extractedText.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const cropDocument = async () => {
  console.log('🔄 cropDocument called')
  if (!uploadedImage.value || !isLibraryReady.value || !scanner.value) {
    errorMessage.value = 'Scanner not ready. Please wait for the library to load.'
    console.log('❌ Scanner not ready:', { uploadedImage: !!uploadedImage.value, isLibraryReady: isLibraryReady.value, scanner: !!scanner.value })
    return
  }

  isProcessing.value = true
  errorMessage.value = ''
  console.log('🔄 Starting document processing...')

  try {
    // Create image element for JScanify
    const img = new Image()

    await new Promise((resolve, reject) => {
      img.onload = resolve
      img.onerror = reject
      img.src = uploadedImage.value
    })

    currentImageElement.value = img

    // Use JScanify to highlight paper detection
    console.log('🔍 Detecting document boundaries...')
    const highlightedCanvas = scanner.value.highlightPaper(img)

    // Display highlighted detection
    if (highlightCanvas.value) {
      highlightCanvas.value.width = highlightedCanvas.width
      highlightCanvas.value.height = highlightedCanvas.height
      const ctx = highlightCanvas.value.getContext('2d')
      ctx.drawImage(highlightedCanvas, 0, 0)
    }

    // Extract and crop the document
    console.log('✂️ Extracting document...')
    const paperWidth = 800
    const paperHeight = 1000
    const extractedCanvas = scanner.value.extractPaper(img, paperWidth, paperHeight)

    // Convert to data URL for download (always set this)
    croppedImage.value = extractedCanvas.toDataURL('image/jpeg', 0.9)
    console.log('✅ Cropped image data URL set:', croppedImage.value ? 'Success' : 'Failed')

    // Display extracted document in canvas if available
    if (extractCanvas.value) {
      extractCanvas.value.width = extractedCanvas.width
      extractCanvas.value.height = extractedCanvas.height
      const ctx = extractCanvas.value.getContext('2d')
      ctx.drawImage(extractedCanvas, 0, 0)
    }

    successMessage.value = '✅ Document scanned and cropped successfully!'
    console.log('✅ Document processing completed')

  } catch (error) {
    console.error('❌ Document processing error:', error)
    errorMessage.value = 'Error processing document: ' + error.message
  } finally {
    isProcessing.value = false
  }
}

const downloadCropped = () => {
  if (!croppedImage.value) return

  const link = document.createElement('a')
  link.download = 'cropped-document.jpg'
  link.href = croppedImage.value
  link.click()
}

const extractText = async () => {
  if (!croppedImage.value) return

  isExtractingText.value = true
  errorMessage.value = ''

  try {
    // Simulate OCR processing
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Simulated OCR result - in a real implementation, you would use Tesseract.js or similar
    const sampleTexts = [
      "This is a sample document with extracted text.\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n\nUt enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      "INVOICE\n\nDate: 2024-01-15\nInvoice #: INV-001\n\nBill To:\nJohn Doe\n123 Main Street\nAnytown, ST 12345\n\nDescription: Web Development Services\nAmount: $1,500.00\n\nTotal: $1,500.00",
      "MEETING NOTES\n\nDate: June 24, 2025\nAttendees: Alice, Bob, Charlie\n\nAgenda:\n1. Project status update\n2. Budget review\n3. Next steps\n\nAction Items:\n- Alice: Complete design mockups\n- Bob: Review technical specifications\n- Charlie: Schedule client meeting"
    ]

    extractedText.value = sampleTexts[Math.floor(Math.random() * sampleTexts.length)]
    successMessage.value = 'Text extracted successfully!'

  } catch (error) {
    errorMessage.value = 'Error extracting text: ' + error.message
  } finally {
    isExtractingText.value = false
  }
}

const copyText = async () => {
  if (!extractedText.value) return

  try {
    await navigator.clipboard.writeText(extractedText.value)
    successMessage.value = 'Text copied to clipboard!'
  } catch (error) {
    errorMessage.value = 'Failed to copy text to clipboard'
  }
}

</script>

<style scoped>
.jscanify-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
}

.header p {
  font-size: 1.2em;
  opacity: 0.9;
}

.input-section {
  padding: 30px;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s;
}

.btn-primary {
  background: #42b883;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #369870;
  transform: translateY(-2px);
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-2px);
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 6px;
  margin: 20px 30px;
  border-left: 4px solid #dc3545;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 6px;
  margin: 20px 30px;
  border-left: 4px solid #28a745;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #42b883;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Document Scanner Styles */
.upload-area {
  border: 2px dashed #42b883;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  background: #f8fffe;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.upload-area:hover {
  border-color: #369870;
  background: #f0fffe;
}

.upload-placeholder {
  cursor: pointer;
  padding: 20px;
}

.upload-icon {
  font-size: 4em;
  margin-bottom: 20px;
  color: #42b883;
}

.upload-hint {
  color: #6c757d;
  font-size: 14px;
  margin-top: 10px;
}

.image-preview {
  max-width: 100%;
}

.image-preview img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.image-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.cropped-result {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.cropped-result h3 {
  color: #35495e;
  margin-bottom: 20px;
  text-align: center;
}

.cropped-preview {
  text-align: center;
}

.cropped-preview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.crop-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Document Scan Results */
.scan-results {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.result-item {
  margin-bottom: 30px;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-item h3 {
  color: #35495e;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1.2em;
}

.canvas-container {
  text-align: center;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-canvas {
  max-width: 100%;
  max-height: 400px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.extracted-text {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.extracted-text h3 {
  color: #35495e;
  margin-bottom: 20px;
  text-align: center;
}

.text-content {
  position: relative;
}

.text-content textarea {
  width: 100%;
  height: 200px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  margin-bottom: 15px;
}

.text-content button {
  display: block;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-tabs {
    flex-direction: column;
  }

  .feature-tab {
    padding: 15px 20px;
  }

  .button-group,
  .image-actions,
  .crop-actions {
    flex-direction: column;
    align-items: center;
  }

  .button-group button,
  .image-actions button,
  .crop-actions button {
    width: 200px;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
