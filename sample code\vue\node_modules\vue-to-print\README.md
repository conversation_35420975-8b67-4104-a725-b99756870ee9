# vue-to-print

You've created a Vue component and would love to give end users the ability to print out the contents of that component. This package aims to solve that by popping up a print window with CSS styles copied over as well.

![npm](https://img.shields.io/npm/v/vue-to-print?style=flat-square)
![npm](https://img.shields.io/npm/dm/vue-to-print?style=flat-square)
![NPM](https://img.shields.io/npm/l/vue-to-print?style=flat-square)
[![semantic-release: angular](https://img.shields.io/badge/semantic--release-angular-e10079?logo=semantic-release&style=flat-square)](https://github.com/semantic-release/semantic-release)
[![Netlify Status](https://api.netlify.com/api/v1/badges/133df312-97ec-411b-816e-52ee0bd49808/deploy-status?style=flat-square)](https://app.netlify.com/sites/vue-to-print/deploys)

## What is this?

This is the Vue 3.x version of [ReactToPrint](https://github.com/gregnb/react-to-print).

> Thanks to the [contributors](https://github.com/gregnb/react-to-print/graphs/contributors) of `ReactToPrint` whose work made this package possible.

VueToPrint aims to be as consistent as possible with ReactToPrint so that you can use the same API. However, there are some differences due to the differences between Vue 3.x and React.

## Documentation

- [Online Documentation](https://vue-to-print.netlify.app/)

### Example

Click any of the buttons below to start a new development environment to demo or contribute to the codebase without having to install anything on your machine:

###### Recommended 
[![Open in CodeSandbox](https://codesandbox.io/static/img/play-codesandbox.svg)](https://codesandbox.io/p/devbox/github/siaikin/vue-to-print-example?embed=1)

###### Untested
[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/siaikin/vue-to-print-example)

[![Open in Repl.it](https://replit.com/badge/github/withastro/astro)](https://replit.com/github/siaikin/vue-to-print-example)

[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/siaikin/vue-to-print-example)

[![Open in Codeanywhere](https://codeanywhere.com/img/open-in-codeanywhere-btn.svg)](https://app.codeanywhere.com/#https://github.com/siaikin/vue-to-print-example)

[![Open in Gitpod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/siaikin/vue-to-print-example)

[![Open in VS Code](https://img.shields.io/badge/Open%20in-VS%20Code-blue?logo=visualstudiocode)](https://vscode.dev/github/siaikin/vue-to-print-example)

[![Open in Glitch](https://img.shields.io/badge/Open%20in-Glitch-blue?logo=glitch)](https://glitch.com/edit/#!/import/github/siaikin/vue-to-print-example)



## Thanks
![JetBrains Logo (Main) logo](https://resources.jetbrains.com/storage/products/company/brand/logos/jb_beam.svg)
> Thank JetBrains for providing an [open-source license](https://jb.gg/OpenSourceSupport), which allows me to use such an excellent IDE.

## License
[![FOSSA Status](https://app.fossa.com/api/projects/git%2Bgithub.com%2Fsiaikin%2Fvue-to-print.svg?type=large&issueType=license)](https://app.fossa.com/projects/git%2Bgithub.com%2Fsiaikin%2Fvue-to-print?ref=badge_large&issueType=license)
