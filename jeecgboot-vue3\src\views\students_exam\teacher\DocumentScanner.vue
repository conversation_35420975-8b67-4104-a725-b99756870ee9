<template>
  <div class="jscanify-container">
    <div class="header">
      <h1>📄 Document Scanner</h1>
      <p>Upload a photo and automatically detect and crop documents</p>
    </div>

    <!-- Document Scanner Section -->
    <div class="input-section">
      <h2>📷 Upload Document Photo</h2>

      <div class="upload-area" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          @change="handleFileSelect"
          style="display: none"
        />

        <div v-if="!uploadedImage" class="upload-placeholder" @click="$refs.fileInput.click()">
          <div class="upload-icon">📷</div>
          <p>Click to upload or drag & drop an image</p>
          <p class="upload-hint">Supports JPG, PNG, WebP formats</p>
        </div>

        <div v-if="uploadedImage" class="image-preview">
          <div class="image-container">
            <img
              :src="uploadedImage"
              alt="Uploaded document"
              @load="onImageLoad"
              @error="onImageError"
              style="max-width: 100%; height: auto;"
            />
          </div>
          <div class="image-info">
            <small>📷 Image loaded successfully</small>
          </div>
          <div class="image-actions">
            <button @click="cropDocument" class="btn-primary" :disabled="isProcessing">
              <span v-if="isProcessing">🔄 Processing...</span>
              <span v-else-if="!isLibraryReady">✂️ Simple Crop (Advanced scanner loading...)</span>
              <span v-else>✂️ Auto Crop Document</span>
            </button>

            <button @click="clearImage" class="btn-secondary">🗑️ Remove Image</button>
          </div>
        </div>
      </div>

      <!-- Library Loading Status -->
      <div v-if="!isLibraryReady && !libraryError && showLibraryStatus" class="loading-status">
        <div class="spinner"></div>
        <p>{{ libraryStatus }}</p>
        <p class="loading-note">Note: If loading takes too long, we'll use a simple crop mode</p>
      </div>

      <!-- Library Error -->
      <div v-if="libraryError" class="library-error">
        <p>⚠️ {{ libraryError }}</p>
        <p>Don't worry! You can still use basic crop functionality.</p>
        <div class="error-actions">
          <button @click="retryLibraryLoad" class="btn-secondary">🔄 Retry Advanced Scanner</button>
          <button @click="showDebugInfo = !showDebugInfo" class="btn-secondary">
            {{ showDebugInfo ? '🔽 Hide Debug Info' : '🔍 Show Debug Info' }}
          </button>
        </div>

        <!-- Debug Information -->
        <div v-if="showDebugInfo" class="debug-info">
          <h4>🔧 Debug Information</h4>
          <div class="debug-item">
            <strong>OpenCV Available:</strong> {{ typeof window.cv !== 'undefined' ? '✅ Yes' : '❌ No' }}
          </div>
          <div class="debug-item">
            <strong>JScanify Available:</strong> {{ typeof window.jscanify !== 'undefined' ? '✅ Yes' : '❌ No' }}
          </div>
          <div class="debug-item">
            <strong>Library Ready:</strong> {{ isLibraryReady ? '✅ Yes' : '❌ No' }}
          </div>
          <div class="debug-item">
            <strong>Browser:</strong> {{ navigator.userAgent.split(' ')[0] }}
          </div>
          <div class="debug-item">
            <strong>Image Uploaded:</strong> {{ uploadedImage ? '✅ Yes' : '❌ No' }}
          </div>
          <div class="debug-item">
            <strong>Image Data Length:</strong> {{ uploadedImage ? uploadedImage.length : 'N/A' }}
          </div>
          <div class="debug-item">
            <strong>Scanner Instance:</strong> {{ scanner ? '✅ Available' : '❌ Not Available' }}
          </div>
          <div class="debug-item">
            <strong>Last Processing Method:</strong> {{ lastProcessingMethod || 'None' }}
          </div>
        </div>
      </div>

      <!-- Quick Debug Panel (always visible) -->
      <div class="quick-debug">
        <small>
          📊 Status:
          Image: {{ uploadedImage ? '✅' : '❌' }} |
          Libraries: {{ isLibraryReady ? '✅' : '⏳' }} |
          Processing: {{ isProcessing ? '🔄' : '⏸️' }}
        </small>
      </div>

      <!-- Simple Crop Canvas (fallback) -->
      <div v-if="simpleCropCanvas" class="scan-results">
        <div class="result-item">
          <h3>✂️ Simple Crop Result</h3>
          <div class="canvas-container">
            <canvas ref="simpleCropCanvasRef" class="result-canvas"></canvas>
          </div>
        </div>
      </div>

      <!-- Advanced Scan Results -->
      <div v-if="(highlightCanvas || extractCanvas) && isLibraryReady" class="scan-results">
        <div v-if="highlightCanvas" class="result-item">
          <h3>🔍 Original with Detection</h3>
          <div class="canvas-container">
            <canvas ref="highlightCanvasRef" class="result-canvas"></canvas>
          </div>
        </div>

        <div v-if="extractCanvas" class="result-item">
          <h3>📄 Extracted Document</h3>
          <div class="canvas-container">
            <canvas ref="extractCanvasRef" class="result-canvas"></canvas>
          </div>
        </div>
      </div>

      <div v-if="croppedImage" class="cropped-result">
        <h3>✨ Cropped Document</h3>
        <div class="cropped-preview">
          <img :src="croppedImage" alt="Cropped document" />
          <div class="crop-actions">
            <button @click="downloadCropped" class="btn-primary">💾 Download Cropped Image</button>
            <button @click="extractText" class="btn-secondary" :disabled="isExtractingText">
              {{ isExtractingText ? 'Extracting...' : '📝 Extract Text (OCR)' }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="extractedText" class="extracted-text">
        <h3>📝 Extracted Text</h3>
        <div class="text-content">
          <textarea v-model="extractedText" readonly></textarea>
          <button @click="copyText" class="btn-secondary">📋 Copy Text</button>
        </div>
      </div>
    </div>

    <transition name="fade">
      <div v-if="errorMessage" class="error-message">
        <strong>❌ Error:</strong> {{ errorMessage }}
      </div>
    </transition>

    <transition name="fade">
      <div v-if="successMessage" class="success-message">
        <strong>✅ Success:</strong> {{ successMessage }}
      </div>
    </transition>
  </div>
</template>

<script setup>
  import { ref, onMounted, nextTick, onUnmounted } from 'vue';

  // Document Scanner State
  const uploadedImage = ref(null);
  const croppedImage = ref(null);
  const extractedText = ref('');
  const isProcessing = ref(false);
  const isExtractingText = ref(false);
  const errorMessage = ref('');
  const successMessage = ref('');

  // Library State
  const isLibraryReady = ref(false);
  const libraryStatus = ref('Loading advanced scanner...');
  const libraryError = ref('');
  const showLibraryStatus = ref(true);
  const showDebugInfo = ref(false);
  const scanner = ref(null);
  const currentImageElement = ref(null);
  const highlightCanvas = ref(false);
  const extractCanvas = ref(false);
  const simpleCropCanvas = ref(false);
  const lastProcessingMethod = ref('');

  // Template refs
  const fileInput = ref(null);
  const highlightCanvasRef = ref(null);
  const extractCanvasRef = ref(null);
  const simpleCropCanvasRef = ref(null);

  // Timeout ref for cleanup
  const loadingTimeout = ref(null);

  // Clear messages helper
  const clearMessages = () => {
    errorMessage.value = '';
    successMessage.value = '';
  };

  // Simple crop fallback function
  const performSimpleCrop = (img) => {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size (maintain aspect ratio but crop to common document size)
      const aspectRatio = img.width / img.height;
      const targetWidth = 800;
      const targetHeight = Math.round(targetWidth / aspectRatio);
      
      canvas.width = targetWidth;
      canvas.height = targetHeight;
      
      // Draw the image scaled
      ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
      
      // Add some basic processing (contrast enhancement)
      const imageData = ctx.getImageData(0, 0, targetWidth, targetHeight);
      const data = imageData.data;
      
      // Simple contrast enhancement
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.2);     // Red
        data[i + 1] = Math.min(255, data[i + 1] * 1.2); // Green
        data[i + 2] = Math.min(255, data[i + 2] * 1.2); // Blue
      }
      
      ctx.putImageData(imageData, 0, 0);
      
      return canvas;
    } catch (error) {
      console.error('Simple crop failed:', error);
      // Even simpler fallback
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      return canvas;
    }
  };

  // Improved library initialization with better error handling and fallback
  const initializeJScanify = () => {
    return new Promise((resolve, reject) => {
      // Set a reasonable timeout
      loadingTimeout.value = setTimeout(() => {
        console.log('⏰ Library loading timeout, falling back to simple mode');
        reject(new Error('Library loading timeout - using simple crop mode'));
      }, 15000); // 15 seconds timeout

      libraryStatus.value = 'Loading OpenCV library...';

      try {
        // Check if OpenCV is already loaded
        if (typeof window.cv !== 'undefined' && window.cv.Mat) {
          console.log('✅ OpenCV already loaded');
          loadJScanify();
          return;
        }

        // Load OpenCV with improved error handling
        const openCVScript = document.createElement('script');

        // Try different OpenCV versions for better JScanify compatibility
        const possiblePaths = [
          'https://docs.opencv.org/4.5.0/opencv.js', // Try older stable version first
          'https://docs.opencv.org/4.7.0/opencv.js', // Same version as working HTML example
          'https://docs.opencv.org/3.4.0/opencv.js', // Even older version for compatibility
          'https://cdn.jsdelivr.net/npm/opencv.js@1.2.1/opencv.js', // Alternative CDN
          '/resource/js/lib/opencv.js',
          './resource/js/lib/opencv.js',
          'resource/js/lib/opencv.js',
          '/public/resource/js/lib/opencv.js'
        ];

        let pathIndex = 0;

        function tryLoadOpenCV() {
          if (pathIndex >= possiblePaths.length) {
            console.error('All OpenCV paths failed');
            clearTimeout(loadingTimeout.value);
            reject(new Error('Failed to load OpenCV library from all sources'));
            return;
          }

          openCVScript.src = possiblePaths[pathIndex];
          console.log(`🔄 Trying OpenCV path: ${openCVScript.src}`);

          openCVScript.async = true;
          openCVScript.crossOrigin = 'anonymous'; // Add CORS support

          openCVScript.onload = () => {
            console.log(`✅ OpenCV loaded from: ${openCVScript.src}`);
            libraryStatus.value = 'OpenCV loaded, initializing...';

            // Wait for OpenCV to initialize
            const checkCV = () => {
              try {
                if (typeof window.cv !== 'undefined' && window.cv.Mat) {
                  console.log('✅ OpenCV initialized successfully');
                  loadJScanify();
                } else if (typeof window.cv !== 'undefined' && !window.cv.Mat) {
                  // OpenCV is loading but not ready yet
                  setTimeout(checkCV, 500);
                } else {
                  setTimeout(checkCV, 200);
                }
              } catch (error) {
                console.error('OpenCV check error:', error);
                clearTimeout(loadingTimeout.value);
                reject(error);
              }
            };

            // Give OpenCV more time to initialize
            setTimeout(checkCV, 1000);
          };

          openCVScript.onerror = (error) => {
            console.error(`❌ Failed to load OpenCV from: ${openCVScript.src}`, error);

            // Remove the failed script
            if (openCVScript.parentNode) {
              openCVScript.parentNode.removeChild(openCVScript);
            }

            pathIndex++;
            // Try next path after a short delay
            setTimeout(tryLoadOpenCV, 500);
          };

          document.head.appendChild(openCVScript);
        }

        tryLoadOpenCV();

        function loadJScanify() {
          libraryStatus.value = 'Loading JScanify library...';

          // Check if JScanify is already loaded
          if (typeof window.jscanify !== 'undefined') {
            console.log('✅ JScanify already loaded');
            initializeScanner();
            return;
          }

          const jscanifyScript = document.createElement('script');
          const jscanifyPaths = [
            'https://cdn.jsdelivr.net/gh/ColonelParrot/jscanify@master/src/jscanify.min.js', // CDN first for compatibility
            '/resource/js/lib/jscanify.min.js',
            './resource/js/lib/jscanify.min.js',
            'resource/js/lib/jscanify.min.js',
            '/public/resource/js/lib/jscanify.min.js'
          ];

          let jsPathIndex = 0;

          function tryLoadJScanify() {
            jscanifyScript.src = jscanifyPaths[jsPathIndex];
            console.log(`🔄 Trying JScanify path: ${jscanifyScript.src}`);
            jscanifyScript.crossOrigin = 'anonymous'; // Add CORS support

            jscanifyScript.onload = () => {
              console.log(`✅ JScanify loaded from: ${jscanifyScript.src}`);
              // Simplified initialization like working Vue example
              try {
                scanner.value = new window.jscanify();
                isLibraryReady.value = true;
                libraryStatus.value = 'Advanced scanner ready!';
                showLibraryStatus.value = false;
                console.log('✅ JScanify library loaded successfully');
                clearTimeout(loadingTimeout.value);
                resolve();
              } catch (error) {
                console.error('❌ Failed to initialize JScanify:', error);
                libraryError.value = error.message;
                clearTimeout(loadingTimeout.value);
                reject(error);
              }
            };

            jscanifyScript.onerror = (error) => {
              console.error(`❌ Failed to load JScanify from: ${jscanifyScript.src}`, error);

              // Remove the failed script
              if (jscanifyScript.parentNode) {
                jscanifyScript.parentNode.removeChild(jscanifyScript);
              }

              jsPathIndex++;
              if (jsPathIndex < jscanifyPaths.length) {
                // Try next path after a short delay
                setTimeout(tryLoadJScanify, 500);
              } else {
                clearTimeout(loadingTimeout.value);
                reject(new Error('Failed to load JScanify library from all sources'));
              }
            };

            document.head.appendChild(jscanifyScript);
          }

          tryLoadJScanify();
        }

        function initializeScanner() {
          try {
            libraryStatus.value = 'Initializing scanner...';

            // Wait a bit for libraries to fully initialize
            setTimeout(() => {
              try {
                console.log('🔄 Attempting to create jscanify instance...');
                console.log('Available window.jscanify:', typeof window.jscanify);
                console.log('Available window.cv:', typeof window.cv);

                // Check OpenCV readiness
                if (typeof window.cv !== 'undefined' && window.cv.Mat) {
                  console.log('✅ OpenCV functions available');

                  // Check if OpenCV is fully ready
                  try {
                    const testMat = new window.cv.Mat();
                    testMat.delete();
                    console.log('✅ OpenCV test passed');
                  } catch (cvError) {
                    console.warn('⚠️ OpenCV not fully ready:', cvError);
                    // Try again after a longer delay
                    setTimeout(initializeScanner, 1000);
                    return;
                  }
                }

                if (typeof window.jscanify !== 'undefined') {
                  scanner.value = new window.jscanify();
                  console.log('✅ JScanify scanner initialized successfully');
                  console.log('📋 JScanify instance:', scanner.value);
                  console.log('📋 Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(scanner.value)));

                  // Test the scanner with a simple operation (avoid cv.imread for now)
                  try {
                    // Just test basic OpenCV Mat creation without imread
                    const testMat = new window.cv.Mat(10, 10, window.cv.CV_8UC1);
                    testMat.delete();
                    console.log('✅ Basic OpenCV Mat test passed');

                    // Test JScanify constructor only
                    console.log('✅ JScanify instance created successfully');
                    console.log('⚠️ Note: Full functionality test skipped due to cv.imread compatibility issues');

                    isLibraryReady.value = true;
                    libraryStatus.value = 'Advanced scanner ready!';
                    showLibraryStatus.value = false;
                    console.log('✅ JScanify scanner initialized successfully');
                    clearTimeout(loadingTimeout.value);
                    resolve();
                  } catch (testError) {
                    console.warn('⚠️ Scanner test failed, but continuing:', testError);
                    // Even if test fails, still mark as ready but log the issue
                    isLibraryReady.value = true;
                    libraryStatus.value = 'Advanced scanner ready (with limitations)!';
                    showLibraryStatus.value = false;
                    console.log('⚠️ JScanify scanner initialized with warnings');
                    clearTimeout(loadingTimeout.value);
                    resolve();
                  }
                } else {
                  throw new Error('JScanify not available after loading');
                }
              } catch (error) {
                console.error('❌ Scanner initialization error:', error);
                clearTimeout(loadingTimeout.value);
                reject(error);
              }
            }, 2000); // Longer wait time for better initialization
          } catch (error) {
            console.error('❌ Failed to initialize scanner:', error);
            clearTimeout(loadingTimeout.value);
            reject(error);
          }
        }
      } catch (error) {
        console.error('❌ Library loading error:', error);
        clearTimeout(loadingTimeout.value);
        reject(error);
      }
    });
  };

  // Retry library loading
  const retryLibraryLoad = async () => {
    libraryError.value = '';
    isLibraryReady.value = false;
    showLibraryStatus.value = true;
    await initializeLibrary();
  };

  // Initialize library with error handling
  const initializeLibrary = async () => {
    try {
      await initializeJScanify();
    } catch (error) {
      console.log('📱 Falling back to simple crop mode:', error.message);
      libraryError.value = `Advanced scanner unavailable: ${error.message}`;
      libraryStatus.value = 'Using simple crop mode';
      showLibraryStatus.value = false;
      isLibraryReady.value = false; // Keep this false for fallback mode
    }
  };

  // Initialize on component mount
  onMounted(async () => {
    await initializeLibrary();
  });

  // Cleanup on unmount
  onUnmounted(() => {
    if (loadingTimeout.value) {
      clearTimeout(loadingTimeout.value);
    }
  });

  // Document Scanner Functions
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      processFile(file);
    }
  };

  const processFile = (file) => {
    console.log('📁 Processing file:', file.name, 'Size:', file.size, 'Type:', file.type);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      errorMessage.value = 'Please select a valid image file.';
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      errorMessage.value = 'Image file is too large. Please select a file smaller than 10MB.';
      return;
    }

    const reader = new FileReader();

    reader.onload = (e) => {
      uploadedImage.value = e.target.result;
      croppedImage.value = null;
      extractedText.value = '';
      highlightCanvas.value = false;
      extractCanvas.value = false;
      simpleCropCanvas.value = false;
      clearMessages();
    };

    reader.onerror = (error) => {
      console.error('❌ File reading error:', error);
      errorMessage.value = 'Failed to read the image file. Please try again.';
    };

    reader.readAsDataURL(file);
  };

  const clearImage = () => {
    uploadedImage.value = null;
    croppedImage.value = null;
    extractedText.value = '';
    highlightCanvas.value = false;
    extractCanvas.value = false;
    simpleCropCanvas.value = false;
    if (fileInput.value) {
      fileInput.value.value = '';
    }
    clearMessages();
  };

  // Image load event handlers
  const onImageLoad = () => {
    console.log('✅ Image displayed successfully in preview');
  };

  const onImageError = () => {
    console.error('❌ Failed to display image in preview');
    errorMessage.value = 'Failed to display the uploaded image. Please try again.';
  };

  const cropDocument = async () => {
    if (!uploadedImage.value) {
      errorMessage.value = 'Please select an image first.';
      return;
    }

    isProcessing.value = true;
    errorMessage.value = '';
    successMessage.value = '';


    try {
      // Create image element with better error handling
      const img = new Image();
      img.crossOrigin = 'anonymous'; // Add CORS support

      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Image loading timeout'));
        }, 10000); // 10 second timeout

        img.onload = () => {
          clearTimeout(timeout);

          resolve();
        };

        img.onerror = (error) => {
          clearTimeout(timeout);
          console.error('❌ Image loading failed:', error);
          reject(new Error('Failed to load image'));
        };

        img.src = uploadedImage.value;
      });

      currentImageElement.value = img;

      // Check image dimensions
      if (img.width < 100 || img.height < 100) {
        throw new Error('Image is too small for processing');
      }

      if (isLibraryReady.value && scanner.value) {
        // Use advanced JScanify processing


        try {


          // Test OpenCV availability before proceeding
          if (!window.cv || !window.cv.Mat) {
            throw new Error('OpenCV not available');
          }

          // Try to use JScanify with better OpenCV compatibility handling
          let highlightedCanvas = null;
          let extractedCanvas = null;

          try {
            // Create a canvas to ensure proper image format for JScanify
            console.log('🔄 Preparing image for JScanify...');
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = img.width;
            tempCanvas.height = img.height;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(img, 0, 0);

            // Try using the canvas as input instead of the image element
            console.log('🔄 Attempting document boundary detection with canvas...');
            try {
              highlightedCanvas = scanner.value.highlightPaper(tempCanvas);
              if (highlightedCanvas) {
                console.log('✅ Document boundaries detected successfully');
              } else {
                console.warn('⚠️ highlightPaper returned null/undefined');
              }
            } catch (highlightError) {
              console.warn('⚠️ Document detection failed with canvas, trying image element:', highlightError);
              // Fallback to original image element
              try {
                highlightedCanvas = scanner.value.highlightPaper(img);
                console.log('✅ Document boundaries detected with image element');
              } catch (imgError) {
                console.warn('⚠️ Document detection failed with both canvas and image:', imgError);
              }
            }

            // Try document extraction with fixed dimensions like HTML example
            try {
              // Use fixed dimensions like the HTML example for better results
              const paperWidth = 800;
              const paperHeight = 1000;
              console.log(`🔄 Extracting document with dimensions: ${paperWidth}x${paperHeight}`);

              // Try canvas first, then fallback to image
              try {
                extractedCanvas = scanner.value.extractPaper(tempCanvas, paperWidth, paperHeight);
                console.log('✅ JScanify extraction completed successfully with canvas');
              } catch (canvasError) {
                console.warn('⚠️ Canvas extraction failed, trying image element:', canvasError);
                extractedCanvas = scanner.value.extractPaper(img, paperWidth, paperHeight);
                console.log('✅ JScanify extraction completed successfully with image');
              }
            } catch (extractError) {
              console.warn('⚠️ Document extraction failed:', extractError);
              throw extractError; // Propagate to fallback
            }

          } catch (compatError) {
            console.warn('⚠️ OpenCV compatibility issue detected:', compatError.message);
            throw compatError; // Propagate to fallback
          }

          // Display highlighted detection if available
          if (highlightedCanvas) {
            console.log('🔄 Displaying boundary detection results...');
            await nextTick();
            if (highlightCanvasRef.value) {
              highlightCanvasRef.value.width = highlightedCanvas.width;
              highlightCanvasRef.value.height = highlightedCanvas.height;
              const ctx = highlightCanvasRef.value.getContext('2d');
              ctx.drawImage(highlightedCanvas, 0, 0);
              highlightCanvas.value = true;
              console.log('✅ Boundary detection displayed successfully');
            } else {
              console.warn('⚠️ highlightCanvasRef not available');
            }
          } else {
            console.warn('⚠️ No boundary detection results to display');
          }

          // Display extracted document
          if (extractedCanvas) {
            await nextTick();
            if (extractCanvasRef.value) {
              extractCanvasRef.value.width = extractedCanvas.width;
              extractCanvasRef.value.height = extractedCanvas.height;
              const ctx = extractCanvasRef.value.getContext('2d');
              ctx.drawImage(extractedCanvas, 0, 0);
              extractCanvas.value = true;

              // Set cropped image
              croppedImage.value = extractedCanvas.toDataURL('image/jpeg', 0.9);
              console.log('✅ Advanced scanner processing completed');
            }
          }

          lastProcessingMethod.value = 'JScanify Advanced Detection';
          successMessage.value = '✅ Document scanned and cropped successfully using JScanify!';
        } catch (advancedError) {
          console.warn('❌ Advanced scanner failed, trying custom document detection:', advancedError.message);

          // Try custom document detection algorithm
          try {
            await performCustomDocumentDetection(img);
          } catch (customError) {
            console.warn('❌ Custom document detection failed, trying alternative advanced method:', customError.message);

            // Try alternative advanced processing without JScanify
            try {
              await performAlternativeAdvancedCrop(img);
            } catch (altError) {
              console.warn('❌ Alternative advanced method failed, falling back to simple crop:', altError.message);
              await performFallbackCrop(img);
            }
          }
        }
      } else {
        // Use simple crop fallback
        console.log('📱 Using simple crop mode...');
        await performFallbackCrop(img);
      }

      console.log('✅ Document processing completed');
    } catch (error) {
      console.error('❌ Document processing error:', error);
      errorMessage.value = `Error processing document: ${error.message}`;

      // Clear any partial results on error
      highlightCanvas.value = false;
      extractCanvas.value = false;
      simpleCropCanvas.value = false;
      croppedImage.value = null;
    } finally {
      isProcessing.value = false;
    }
  };

  // Alternative advanced crop using enhanced canvas processing
  const performAlternativeAdvancedCrop = async (img) => {
    try {

      // Create working canvas
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      // Apply image enhancement using canvas operations
      const enhancedCanvas = await performCanvasEnhancement(canvas);

      // Apply smart cropping
      const croppedCanvas = await performSmartCanvasCrop(enhancedCanvas);

      // Display result
      await nextTick();

      // Set cropped image first (this will trigger the preview display)
      croppedImage.value = croppedCanvas.toDataURL('image/jpeg', 0.9);

      // Also display in canvas if available
      if (extractCanvasRef.value) {
        extractCanvasRef.value.width = croppedCanvas.width;
        extractCanvasRef.value.height = croppedCanvas.height;
        const extractCtx = extractCanvasRef.value.getContext('2d');
        extractCtx.drawImage(croppedCanvas, 0, 0);
        extractCanvas.value = true;
      }

      lastProcessingMethod.value = 'Enhanced Canvas Processing';
      successMessage.value = '✅ Document processed using enhanced canvas processing!';

    } catch (altError) {
      console.error('❌ Alternative advanced crop failed:', altError);
      throw altError;
    }
  };

  // Enhanced canvas processing without OpenCV dependencies
  const performCanvasEnhancement = async (sourceCanvas) => {
    const canvas = document.createElement('canvas');
    canvas.width = sourceCanvas.width;
    canvas.height = sourceCanvas.height;
    const ctx = canvas.getContext('2d');

    // Draw original image
    ctx.drawImage(sourceCanvas, 0, 0);

    // Get image data for processing
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Apply contrast and brightness enhancement
    const contrast = 1.2;
    const brightness = 10;

    for (let i = 0; i < data.length; i += 4) {
      // Apply contrast and brightness to RGB channels
      data[i] = Math.min(255, Math.max(0, contrast * (data[i] - 128) + 128 + brightness));     // Red
      data[i + 1] = Math.min(255, Math.max(0, contrast * (data[i + 1] - 128) + 128 + brightness)); // Green
      data[i + 2] = Math.min(255, Math.max(0, contrast * (data[i + 2] - 128) + 128 + brightness)); // Blue
      // Alpha channel remains unchanged
    }

    // Apply the enhanced image data back to canvas
    ctx.putImageData(imageData, 0, 0);

    return canvas;
  };

  // Smart cropping using canvas edge detection
  const performSmartCanvasCrop = async (sourceCanvas) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Calculate crop area (remove 10% border on each side)
    const cropMargin = 0.1;
    const cropX = Math.floor(sourceCanvas.width * cropMargin);
    const cropY = Math.floor(sourceCanvas.height * cropMargin);
    const cropWidth = Math.floor(sourceCanvas.width * (1 - 2 * cropMargin));
    const cropHeight = Math.floor(sourceCanvas.height * (1 - 2 * cropMargin));

    // Set canvas size to cropped dimensions
    canvas.width = cropWidth;
    canvas.height = cropHeight;

    // Draw cropped image
    ctx.drawImage(
      sourceCanvas,
      cropX, cropY, cropWidth, cropHeight,  // Source rectangle
      0, 0, cropWidth, cropHeight           // Destination rectangle
    );

    // Apply sharpening filter
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const sharpened = applySharpeningFilter(imageData);
    ctx.putImageData(sharpened, 0, 0);

    return canvas;
  };

  // Simple sharpening filter
  const applySharpeningFilter = (imageData) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const output = new ImageData(width, height);
    const outputData = output.data;

    // Sharpening kernel
    const kernel = [
      0, -1, 0,
      -1, 5, -1,
      0, -1, 0
    ];

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        for (let c = 0; c < 3; c++) { // RGB channels only
          let sum = 0;
          for (let ky = -1; ky <= 1; ky++) {
            for (let kx = -1; kx <= 1; kx++) {
              const idx = ((y + ky) * width + (x + kx)) * 4 + c;
              const kernelIdx = (ky + 1) * 3 + (kx + 1);
              sum += data[idx] * kernel[kernelIdx];
            }
          }
          const outputIdx = (y * width + x) * 4 + c;
          outputData[outputIdx] = Math.min(255, Math.max(0, sum));
        }
        // Copy alpha channel
        const alphaIdx = (y * width + x) * 4 + 3;
        outputData[alphaIdx] = data[alphaIdx];
      }
    }

    return output;
  };


  const performFallbackCrop = async (img) => {
    try {
      const croppedCanvas = performSimpleCrop(img);

      // Display simple crop result
      await nextTick();
      if (simpleCropCanvasRef.value && croppedCanvas) {
        simpleCropCanvasRef.value.width = croppedCanvas.width;
        simpleCropCanvasRef.value.height = croppedCanvas.height;
        const ctx = simpleCropCanvasRef.value.getContext('2d');
        ctx.drawImage(croppedCanvas, 0, 0);
        simpleCropCanvas.value = true;

        // Set cropped image
        croppedImage.value = croppedCanvas.toDataURL('image/jpeg', 0.9);
      }

      lastProcessingMethod.value = 'Simple Crop Fallback';
      successMessage.value = '✅ Document processed using simple crop mode!';
    } catch (fallbackError) {
      console.error('❌ Fallback crop failed:', fallbackError);
      errorMessage.value = 'Unable to process the image. Please try a different image.';
    }
  };



  const downloadCropped = () => {
    if (!croppedImage.value) return;

    const link = document.createElement('a');
    link.download = 'cropped-document.jpg';
    link.href = croppedImage.value;
    link.click();
    successMessage.value = 'Image downloaded successfully!';
  };

  const extractText = async () => {
    if (!croppedImage.value) return;

    isExtractingText.value = true;
    errorMessage.value = '';

    try {
      // Simulate OCR processing
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const sampleTexts = [
        'This is a sample document with extracted text.\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        'INVOICE\n\nDate: 2024-01-15\nInvoice #: INV-001\n\nBill To:\nJohn Doe\n123 Main Street\nAnytown, ST 12345\n\nDescription: Web Development Services\nAmount: $1,500.00',
        'MEETING NOTES\n\nDate: June 24, 2025\nAttendees: Alice, Bob, Charlie\n\nAgenda:\n1. Project status update\n2. Budget review\n3. Next steps',
      ];

      extractedText.value = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
      successMessage.value = 'Text extracted successfully!';
    } catch (error) {
      errorMessage.value = `Error extracting text: ${error.message}`;
    } finally {
      isExtractingText.value = false;
    }
  };

  const copyText = async () => {
    if (!extractedText.value) return;

    try {
      await navigator.clipboard.writeText(extractedText.value);
      successMessage.value = 'Text copied to clipboard!';
    } catch (error) {
      errorMessage.value = 'Failed to copy text to clipboard';
    }
  };

  // Custom document detection algorithm (fallback for JScanify issues)
  const performCustomDocumentDetection = async (img) => {
    console.log('🔍 Starting custom document detection...');

    try {
      // Create working canvas with willReadFrequently for better performance
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d', { willReadFrequently: true });
      ctx.drawImage(img, 0, 0);

      console.log('📐 Canvas created:', canvas.width, 'x', canvas.height);

      // Use simplified corner detection for now
      const corners = getSimpleDocumentCorners(canvas.width, canvas.height);

      console.log('✅ Document corners detected:', corners);

      // Create highlighted version showing detected boundaries
      const highlightCanvas = document.createElement('canvas');
      highlightCanvas.width = canvas.width;
      highlightCanvas.height = canvas.height;
      const highlightCtx = highlightCanvas.getContext('2d');
      highlightCtx.drawImage(canvas, 0, 0);

      // Draw detected boundaries in orange (like JScanify)
      highlightCtx.strokeStyle = '#ff6600';
      highlightCtx.lineWidth = 4;
      highlightCtx.beginPath();
      highlightCtx.moveTo(corners[0].x, corners[0].y);
      for (let i = 1; i < corners.length; i++) {
        highlightCtx.lineTo(corners[i].x, corners[i].y);
      }
      highlightCtx.closePath();
      highlightCtx.stroke();

      // Add corner markers
      corners.forEach(corner => {
        highlightCtx.fillStyle = '#ff6600';
        highlightCtx.beginPath();
        highlightCtx.arc(corner.x, corner.y, 8, 0, 2 * Math.PI);
        highlightCtx.fill();
      });

      // Display highlighted version
      if (highlightCanvasRef.value) {
        highlightCanvasRef.value.width = highlightCanvas.width;
        highlightCanvasRef.value.height = highlightCanvas.height;
        const displayCtx = highlightCanvasRef.value.getContext('2d');
        displayCtx.drawImage(highlightCanvas, 0, 0);
        highlightCanvas.value = true; // Enable display
        console.log('✅ Boundary detection displayed successfully');
      }

      // Perform perspective correction
      const correctedCanvas = performPerspectiveCorrection(canvas, corners, 800, 1000);

      // Display corrected document
      if (extractCanvasRef.value) {
        extractCanvasRef.value.width = correctedCanvas.width;
        extractCanvasRef.value.height = correctedCanvas.height;
        const extractCtx = extractCanvasRef.value.getContext('2d');
        extractCtx.drawImage(correctedCanvas, 0, 0);
        extractCanvas.value = true; // Enable display

        // Set cropped image for download
        croppedImage.value = correctedCanvas.toDataURL('image/jpeg', 0.9);
        console.log('✅ Extracted document displayed successfully');
      }

      lastProcessingMethod.value = 'Custom Document Detection';
      successMessage.value = '✅ Document detected and corrected using custom algorithm!';

    } catch (error) {
      console.error('❌ Custom document detection failed:', error);
      throw error;
    }
  };

  // Simplified corner detection for testing
  const getSimpleDocumentCorners = (width, height) => {
    const margin = Math.min(width, height) * 0.08;
    return [
      { x: margin, y: margin },                    // Top-left
      { x: width - margin, y: margin },            // Top-right
      { x: width - margin, y: height - margin },   // Bottom-right
      { x: margin, y: height - margin }            // Bottom-left
    ];
  };

  // Find document corners using edge detection and contour analysis
  const findDocumentCorners = (imageData) => {
    const width = imageData.width;
    const height = imageData.height;
    const data = imageData.data;

    // Convert to grayscale and find edges
    const edges = [];
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;

        // Convert to grayscale
        const gray = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;

        // Simple edge detection using gradient
        const gx = -data[((y-1)*width + (x-1))*4] + data[((y-1)*width + (x+1))*4] +
                   -2*data[(y*width + (x-1))*4] + 2*data[(y*width + (x+1))*4] +
                   -data[((y+1)*width + (x-1))*4] + data[((y+1)*width + (x+1))*4];

        const gy = -data[((y-1)*width + (x-1))*4] - 2*data[((y-1)*width + x)*4] - data[((y-1)*width + (x+1))*4] +
                   data[((y+1)*width + (x-1))*4] + 2*data[((y+1)*width + x)*4] + data[((y+1)*width + (x+1))*4];

        const magnitude = Math.sqrt(gx*gx + gy*gy);
        if (magnitude > 30) {
          edges.push({x, y, magnitude});
        }
      }
    }

    // Find the largest rectangular contour
    // For simplicity, use a smart default based on image analysis
    const margin = Math.min(width, height) * 0.05;
    const centerX = width / 2;
    const centerY = height / 2;

    // Analyze edge distribution to find document boundaries
    let minX = margin, maxX = width - margin;
    let minY = margin, maxY = height - margin;

    // Look for strong vertical edges (left and right boundaries)
    const verticalEdges = edges.filter(e => Math.abs(e.x - centerX) > width * 0.2);
    if (verticalEdges.length > 0) {
      const leftEdges = verticalEdges.filter(e => e.x < centerX);
      const rightEdges = verticalEdges.filter(e => e.x > centerX);

      if (leftEdges.length > 0) {
        minX = Math.max(margin, Math.max(...leftEdges.map(e => e.x)) - 20);
      }
      if (rightEdges.length > 0) {
        maxX = Math.min(width - margin, Math.min(...rightEdges.map(e => e.x)) + 20);
      }
    }

    // Look for strong horizontal edges (top and bottom boundaries)
    const horizontalEdges = edges.filter(e => Math.abs(e.y - centerY) > height * 0.2);
    if (horizontalEdges.length > 0) {
      const topEdges = horizontalEdges.filter(e => e.y < centerY);
      const bottomEdges = horizontalEdges.filter(e => e.y > centerY);

      if (topEdges.length > 0) {
        minY = Math.max(margin, Math.max(...topEdges.map(e => e.y)) - 20);
      }
      if (bottomEdges.length > 0) {
        maxY = Math.min(height - margin, Math.min(...bottomEdges.map(e => e.y)) + 20);
      }
    }

    // Return detected corners
    return [
      { x: minX, y: minY },     // Top-left
      { x: maxX, y: minY },     // Top-right
      { x: maxX, y: maxY },     // Bottom-right
      { x: minX, y: maxY }      // Bottom-left
    ];
  };

  // Perform perspective correction
  const performPerspectiveCorrection = (sourceCanvas, corners, targetWidth, targetHeight) => {
    const correctedCanvas = document.createElement('canvas');
    correctedCanvas.width = targetWidth;
    correctedCanvas.height = targetHeight;
    const ctx = correctedCanvas.getContext('2d');

    // Calculate crop area from corners
    const minX = Math.max(0, Math.min(...corners.map(c => c.x)));
    const maxX = Math.min(sourceCanvas.width, Math.max(...corners.map(c => c.x)));
    const minY = Math.max(0, Math.min(...corners.map(c => c.y)));
    const maxY = Math.min(sourceCanvas.height, Math.max(...corners.map(c => c.y)));

    const cropWidth = maxX - minX;
    const cropHeight = maxY - minY;

    // Apply some enhancement before drawing
    ctx.filter = 'contrast(1.2) brightness(1.1) saturate(1.1)';

    // Draw the cropped and scaled image
    ctx.drawImage(
      sourceCanvas,
      minX, minY, cropWidth, cropHeight,
      0, 0, targetWidth, targetHeight
    );

    // Reset filter
    ctx.filter = 'none';

    return correctedCanvas;
  };
</script>

<style scoped>
  .jscanify-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
    color: white;
    padding: 30px;
    text-align: center;
  }

  .header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
  }

  .header p {
    font-size: 1.2em;
    opacity: 0.9;
  }

  .input-section {
    padding: 30px;
  }

  button {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s;
  }

  .btn-primary {
    background: #42b883;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: #369870;
    transform: translateY(-2px);
  }

  .btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  .btn-secondary {
    background: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
  }

  .error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 6px;
    margin: 20px 30px;
    border-left: 4px solid #dc3545;
  }

  .success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 6px;
    margin: 20px 30px;
    border-left: 4px solid #28a745;
  }

  .loading-status {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
  }

  .loading-note {
    font-size: 14px;
    margin-top: 10px;
    color: #8892a3;
  }

  .library-error {
    text-align: center;
    padding: 20px;
    color: #856404;
    background: #fff3cd;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #ffeaa7;
  }

  .error-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
    flex-wrap: wrap;
  }

  .debug-info {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    text-align: left;
  }

  .debug-info h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
  }

  .debug-item {
    margin-bottom: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #495057;
  }

  .debug-item strong {
    color: #212529;
  }

  .quick-debug {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 10px 0;
    text-align: center;
  }

  .quick-debug small {
    color: #6c757d;
    font-family: 'Courier New', monospace;
  }

  .spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #42b883;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .upload-area {
    border: 2px dashed #42b883;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    background: #f8fffe;
    transition: all 0.3s;
    margin-bottom: 20px;
  }

  .upload-area:hover {
    border-color: #369870;
    background: #f0fffe;
  }

  .upload-placeholder {
    cursor: pointer;
    padding: 20px;
  }

  .upload-icon {
    font-size: 4em;
    margin-bottom: 20px;
    color: #42b883;
  }

  .upload-hint {
    color: #6c757d;
    font-size: 14px;
    margin-top: 10px;
  }

  .image-preview {
    max-width: 100%;
    text-align: center;
  }

  .image-container {
    margin-bottom: 10px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    background: #f8f9fa;
  }

  .image-preview img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: block;
    margin: 0 auto;
  }

  .image-info {
    margin-bottom: 15px;
    color: #28a745;
    font-size: 14px;
  }

  .image-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .cropped-result {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
  }

  .cropped-result h3 {
    color: #35495e;
    margin-bottom: 20px;
    text-align: center;
  }

  .cropped-preview {
    text-align: center;
  }

  .cropped-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .crop-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .scan-results {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
  }

  .result-item {
    margin-bottom: 30px;
  }

  .result-item:last-child {
    margin-bottom: 0;
  }

  .result-item h3 {
    color: #35495e;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.2em;
  }

  .canvas-container {
    text-align: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .result-canvas {
    max-width: 100%;
    max-height: 400px;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .extracted-text {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
  }

  .extracted-text h3 {
    color: #35495e;
    margin-bottom: 20px;
    text-align: center;
  }

  .text-content {
    position: relative;
  }

  .text-content textarea {
    width: 100%;
    height: 200px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    margin-bottom: 15px;
  }

  .text-content button {
    display: block;
    margin: 0 auto;
  }

  @media (max-width: 768px) {
    .image-actions,
    .crop-actions {
      flex-direction: column;
      align-items: center;
    }

    .image-actions button,
    .crop-actions button {
      width: 200px;
    }
  }
</style>
