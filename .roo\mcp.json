{"mcpServers": {"mysql": {"command": "uvx", "args": ["--from", "mysql-mcp-server", "mysql_mcp_server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_USER": "root", "MYSQL_PASSWORD": "123456", "MYSQL_DATABASE": "jeecg-boot"}, "disabled": false, "alwaysAllow": ["execute_sql"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/Projects/Jeecg"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "C:/Projects/Jeecg"]}}}